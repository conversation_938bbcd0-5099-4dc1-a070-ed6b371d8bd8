list=[{
"base": &"CharacterBody3D",
"class": &"AIOpponent",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ai/ai_opponent.gd"
}, {
"base": &"Node",
"class": &"CyberToolsManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/core/cyber_tools_manager.gd"
}, {
"base": &"Node",
"class": &"GameManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/core/game_manager.gd"
}, {
"base": &"Node3D",
"class": &"MainWorld",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/scenes/main_world.gd"
}, {
"base": &"Node",
"class": &"MissionManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/core/mission_manager.gd"
}, {
"base": &"Control",
"class": &"MobileInterface",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/mobile_interface.gd"
}, {
"base": &"Control",
"class": &"PCTerminal",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/pc_terminal.gd"
}, {
"base": &"CharacterBody3D",
"class": &"PlayerController",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/player/player_controller.gd"
}]
