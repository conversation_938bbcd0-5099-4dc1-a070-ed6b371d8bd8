# Cyber Tron - Cybersecurity Educational Game

A GTA-style open-world cybersecurity educational game built with Godot 4. Learn cybersecurity through immersive gameplay!

## 🎮 Game Overview

Cyber Tron is an innovative cybersecurity education game that combines the excitement of open-world gameplay with hands-on cybersecurity training. Players navigate a 3D world, receive missions on their in-game mobile phone, and use real cybersecurity tools through interactive PC terminals.

### Key Features

- **GTA-Style Gameplay**: Third-person open-world exploration
- **Progressive Learning**: Missions scale from beginner to expert level
- **Real Cybersecurity Tools**: Simulated nmap, Wireshark, Hydra, SQLMap, Metasploit, and more
- **Interactive Mobile Interface**: Receive missions and track progress
- **PC Terminal Simulation**: Execute real cybersecurity commands
- **AI Opponents**: Face off against intelligent cyber adversaries
- **Skill Progression**: Develop networking, penetration testing, and other cybersecurity skills

## 🛠️ Technology Stack

- **Game Engine**: Godot 4.x (Open Source)
- **Programming Language**: GDScript
- **Platform**: Windows, Linux, macOS
- **License**: Open Source

## 📁 Project Structure

```
cyber-tron/
├── project.godot              # Main Godot project file
├── scripts/                   # Game logic scripts
│   ├── core/                  # Core game systems
│   │   ├── game_manager.gd    # Main game management
│   │   ├── mission_manager.gd # Mission system
│   │   └── cyber_tools_manager.gd # Cybersecurity tools
│   ├── player/                # Player controller
│   │   └── player_controller.gd
│   ├── ui/                    # User interface
│   │   ├── mobile_interface.gd # In-game phone
│   │   └── pc_terminal.gd     # Terminal interface
│   ├── ai/                    # AI systems
│   │   └── ai_opponent.gd     # AI adversaries
│   └── scenes/                # Scene management
│       └── main_world.gd      # Main world scene
├── scenes/                    # Godot scene files
├── assets/                    # Game assets
└── docs/                      # Documentation
```

## 🎯 Game Mechanics

### Mission System
- **Progressive Difficulty**: Missions start with basic network scanning and advance to complex penetration testing
- **Real-World Scenarios**: Based on actual cybersecurity challenges
- **Skill Development**: Each mission teaches specific cybersecurity concepts

### Cybersecurity Tools
- **Nmap**: Network discovery and security auditing
- **Netcat**: Network utility for connections
- **Wireshark**: Network protocol analysis
- **Hydra**: Password cracking simulation
- **SQLMap**: SQL injection testing
- **Metasploit**: Penetration testing framework

### AI Opponents
- **Script Kiddies**: Beginner-level adversaries
- **Cyber Criminals**: Intermediate threats
- **APT Actors**: Advanced persistent threats
- **Nation State Actors**: Expert-level opponents

## 🚀 Getting Started

### Prerequisites
- Godot 4.x installed
- Basic understanding of cybersecurity concepts (helpful but not required)

### Installation
1. Clone or download this repository
2. Open Godot 4.x
3. Import the project by selecting `project.godot`
4. Press F5 to run the game

### Controls
- **WASD**: Move player
- **Mouse**: Look around
- **E**: Interact with objects
- **T**: Open mobile phone
- **Space**: Jump
- **ESC**: Exit interfaces

## 📚 Learning Objectives

### Beginner Level
- Network discovery with Nmap
- Basic port scanning
- Service identification
- Understanding network protocols

### Intermediate Level
- Vulnerability scanning
- Password security testing
- Web application basics
- Network traffic analysis

### Advanced Level
- Penetration testing methodologies
- Social engineering awareness
- Advanced exploitation techniques
- Incident response

### Expert Level
- Advanced persistent threat simulation
- Complex attack chains
- Forensic analysis
- Security architecture

## 🎓 Educational Value

Cyber Tron provides hands-on experience with:
- **Network Security**: Understanding how networks work and their vulnerabilities
- **Penetration Testing**: Ethical hacking methodologies
- **Incident Response**: How to detect and respond to cyber attacks
- **Security Tools**: Real-world cybersecurity software
- **Risk Assessment**: Identifying and mitigating security risks

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for:
- Code style standards
- Bug reporting
- Feature requests
- Educational content suggestions

## 📄 License

This project is open source and available under the MIT License.

## 🔗 Resources

- [Godot Engine Documentation](https://docs.godotengine.org/)
- [Cybersecurity Learning Resources](https://www.cybrary.it/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)

## 🎯 Mission Examples

### Mission 1: Network Discovery
**Objective**: Use nmap to discover devices on the network
**Commands**: `nmap -sn ***********/24`
**Learning**: Network scanning basics, IP addressing

### Mission 2: Port Scanning
**Objective**: Identify open ports on target systems
**Commands**: `nmap -sS ************`
**Learning**: Port scanning, service identification

### Mission 3: Vulnerability Assessment
**Objective**: Find security vulnerabilities
**Commands**: `nmap --script vuln ************`
**Learning**: Vulnerability scanning, risk assessment

## 🏆 Achievements

- **Script Kiddie**: Complete first network scan
- **Port Scanner**: Discover 10 open ports
- **Vulnerability Hunter**: Find your first CVE
- **Password Cracker**: Successfully crack a weak password
- **Web Hacker**: Exploit a web application
- **APT Hunter**: Detect an advanced persistent threat
- **Cyber Defender**: Successfully defend against all attacks

## 📞 Support

For support, questions, or feedback:
- Create an issue in this repository
- Join our community discussions
- Check the documentation

---

**Disclaimer**: This game is for educational purposes only. All cybersecurity techniques should only be used in authorized environments for learning and legitimate security testing.
