#!/usr/bin/env python3
"""
Cyber Tron - Text-based Demo
A simple text-based demonstration of the Cyber Tron cybersecurity game
while you're setting up Godot for the full 3D experience.
"""

import os
import time
import random

class CyberTronDemo:
    def __init__(self):
        self.player_level = 1
        self.player_score = 0
        self.completed_missions = []
        self.current_mission = None
        self.skills = {
            "networking": 0,
            "penetration_testing": 0,
            "social_engineering": 0,
            "cryptography": 0
        }
        
        self.missions = [
            {
                "id": "mission_001",
                "title": "First Network Scan",
                "description": "Use nmap to discover devices on the network",
                "difficulty": "Beginner",
                "commands": ["nmap -sn ***********/24"],
                "reward": 100
            },
            {
                "id": "mission_002", 
                "title": "Port Discovery",
                "description": "Scan for open ports on target systems",
                "difficulty": "Beginner",
                "commands": ["nmap -sS ***********"],
                "reward": 150
            },
            {
                "id": "mission_003",
                "title": "Service Detection",
                "description": "Identify services running on open ports",
                "difficulty": "Intermediate",
                "commands": ["nmap -sV ***********"],
                "reward": 200
            }
        ]

    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')

    def print_banner(self):
        print("=" * 60)
        print("🎮 CYBER TRON - Cybersecurity Educational Game Demo")
        print("=" * 60)
        print(f"Level: {self.player_level} | Score: {self.player_score}")
        print(f"Skills: Networking({self.skills['networking']}) | PenTest({self.skills['penetration_testing']})")
        print("=" * 60)

    def show_mobile_interface(self):
        print("\n📱 MOBILE INTERFACE")
        print("-" * 30)
        print("Available Missions:")
        
        for i, mission in enumerate(self.missions):
            if mission["id"] not in [m["id"] for m in self.completed_missions]:
                status = "🔴 ACTIVE" if self.current_mission and self.current_mission["id"] == mission["id"] else "⚪ Available"
                print(f"{i+1}. {mission['title']} - {mission['difficulty']} {status}")
        
        print("\nCompleted Missions:")
        for mission in self.completed_missions:
            print(f"✅ {mission['title']}")

    def show_pc_terminal(self):
        print("\n💻 CYBER SECURITY TERMINAL")
        print("-" * 40)
        print("cybertron@security:~$ ")
        print("\nAvailable Commands:")
        print("  help          - Show this help")
        print("  nmap          - Network discovery tool")
        print("  netcat        - Network utility")
        print("  mission       - Show current mission")
        print("  exit          - Exit terminal")
        
        if self.current_mission:
            print(f"\n🎯 Current Mission: {self.current_mission['title']}")
            print(f"Description: {self.current_mission['description']}")
            print(f"Try command: {self.current_mission['commands'][0]}")

    def execute_command(self, command):
        print(f"\ncybertron@security:~$ {command}")
        
        if command.startswith("nmap"):
            return self.simulate_nmap(command)
        elif command.startswith("netcat") or command.startswith("nc"):
            return self.simulate_netcat(command)
        elif command == "help":
            self.show_pc_terminal()
            return True
        elif command == "mission":
            if self.current_mission:
                print(f"Current Mission: {self.current_mission['title']}")
                print(f"Description: {self.current_mission['description']}")
            else:
                print("No active mission. Check your mobile phone!")
            return True
        else:
            print(f"Command not found: {command}")
            print("Type 'help' for available commands")
            return False

    def simulate_nmap(self, command):
        print("Starting Nmap scan...")
        time.sleep(1)
        
        if "-sn" in command:
            print("Nmap scan report:")
            print("Host is up (0.001s latency)")
            print("*********** - Router")
            print("***********0 - Desktop Computer") 
            print("***********5 - Laptop")
            print("************ - Server")
            print("\nNmap done: 4 IP addresses (4 hosts up) scanned")
            self.skills["networking"] += 1
            
        elif "-sS" in command:
            print("PORT     STATE SERVICE")
            print("22/tcp   open  ssh")
            print("80/tcp   open  http")
            print("443/tcp  open  https")
            print("3389/tcp open  ms-wbt-server")
            self.skills["networking"] += 1
            self.skills["penetration_testing"] += 1
            
        elif "-sV" in command:
            print("PORT     STATE SERVICE    VERSION")
            print("22/tcp   open  ssh        OpenSSH 8.2p1")
            print("80/tcp   open  http       Apache httpd 2.4.41")
            print("443/tcp  open  ssl/http   Apache httpd 2.4.41")
            print("3389/tcp open  ms-wbt-server Microsoft Terminal Services")
            self.skills["networking"] += 2
            self.skills["penetration_testing"] += 2
            
        else:
            print("Usage: nmap [options] target")
            print("  -sn    Ping scan")
            print("  -sS    SYN scan") 
            print("  -sV    Version detection")
            return False
            
        return True

    def simulate_netcat(self, command):
        print("Netcat - The network Swiss Army knife")
        print("Connection established...")
        print("SSH-2.0-OpenSSH_8.2p1 Ubuntu-4ubuntu0.5")
        self.skills["networking"] += 1
        return True

    def check_mission_completion(self, command):
        if not self.current_mission:
            return False
            
        for required_cmd in self.current_mission["commands"]:
            if required_cmd.split()[0] in command:
                print(f"\n🎉 MISSION COMPLETED: {self.current_mission['title']}")
                print(f"Reward: +{self.current_mission['reward']} points")
                
                self.player_score += self.current_mission["reward"]
                self.completed_missions.append(self.current_mission)
                self.current_mission = None
                
                if len(self.completed_missions) % 2 == 0:
                    self.player_level += 1
                    print(f"🆙 LEVEL UP! Now Level {self.player_level}")
                
                return True
        return False

    def main_menu(self):
        while True:
            self.clear_screen()
            self.print_banner()
            
            print("\n🎮 MAIN MENU")
            print("1. 📱 Open Mobile Phone (View Missions)")
            print("2. 💻 Access Computer Terminal")
            print("3. 📊 View Player Stats")
            print("4. ❓ About Cyber Tron")
            print("5. 🚪 Exit Demo")
            
            choice = input("\nSelect option (1-5): ").strip()
            
            if choice == "1":
                self.mobile_menu()
            elif choice == "2":
                self.terminal_menu()
            elif choice == "3":
                self.show_stats()
            elif choice == "4":
                self.show_about()
            elif choice == "5":
                print("\nThanks for trying Cyber Tron Demo!")
                print("Install Godot 4.x to play the full 3D game!")
                break
            else:
                print("Invalid choice. Press Enter to continue...")
                input()

    def mobile_menu(self):
        while True:
            self.clear_screen()
            self.print_banner()
            self.show_mobile_interface()
            
            print("\n📱 MOBILE MENU")
            print("1. Select Mission")
            print("2. Back to Main Menu")
            
            choice = input("\nSelect option: ").strip()
            
            if choice == "1":
                self.select_mission()
            elif choice == "2":
                break
            else:
                print("Invalid choice. Press Enter to continue...")
                input()

    def select_mission(self):
        available_missions = [m for m in self.missions if m["id"] not in [cm["id"] for cm in self.completed_missions]]
        
        if not available_missions:
            print("\n🎉 All missions completed! You're a cybersecurity expert!")
            input("Press Enter to continue...")
            return
            
        print("\nSelect a mission:")
        for i, mission in enumerate(available_missions):
            print(f"{i+1}. {mission['title']} - {mission['difficulty']}")
        
        try:
            choice = int(input("\nEnter mission number: ")) - 1
            if 0 <= choice < len(available_missions):
                self.current_mission = available_missions[choice]
                print(f"\n✅ Mission activated: {self.current_mission['title']}")
                print("Go to a computer terminal to complete it!")
            else:
                print("Invalid mission number!")
        except ValueError:
            print("Please enter a valid number!")
        
        input("Press Enter to continue...")

    def terminal_menu(self):
        while True:
            self.clear_screen()
            self.print_banner()
            self.show_pc_terminal()
            
            command = input("\ncybertron@security:~$ ").strip().lower()
            
            if command == "exit":
                break
            elif command == "":
                continue
            else:
                success = self.execute_command(command)
                if success and self.current_mission:
                    self.check_mission_completion(command)
                
                input("\nPress Enter to continue...")

    def show_stats(self):
        self.clear_screen()
        self.print_banner()
        
        print("\n📊 PLAYER STATISTICS")
        print("-" * 30)
        print(f"Level: {self.player_level}")
        print(f"Score: {self.player_score}")
        print(f"Missions Completed: {len(self.completed_missions)}")
        print(f"Current Mission: {self.current_mission['title'] if self.current_mission else 'None'}")
        
        print("\n🎯 SKILLS")
        for skill, level in self.skills.items():
            print(f"{skill.replace('_', ' ').title()}: {level}")
        
        input("\nPress Enter to continue...")

    def show_about(self):
        self.clear_screen()
        print("🎮 ABOUT CYBER TRON")
        print("=" * 50)
        print("Cyber Tron is an educational cybersecurity game that teaches")
        print("real-world cybersecurity skills through immersive gameplay.")
        print()
        print("🎯 LEARNING OBJECTIVES:")
        print("• Network discovery and scanning")
        print("• Port scanning and service identification") 
        print("• Vulnerability assessment")
        print("• Penetration testing methodologies")
        print("• Cybersecurity tool usage")
        print()
        print("🛠️ TOOLS YOU'LL LEARN:")
        print("• Nmap - Network discovery")
        print("• Netcat - Network utility")
        print("• Wireshark - Traffic analysis")
        print("• Hydra - Password testing")
        print("• SQLMap - Web application testing")
        print("• Metasploit - Penetration testing")
        print()
        print("🎮 FULL GAME FEATURES:")
        print("• 3D open-world environment (GTA-style)")
        print("• Interactive mobile phone interface")
        print("• Realistic PC terminals")
        print("• AI opponents with different skill levels")
        print("• Progressive mission system")
        print("• Skill development tracking")
        print()
        print("To play the full 3D game:")
        print("1. Download Godot 4.x from https://godotengine.org/download")
        print("2. Import the project.godot file")
        print("3. Press F5 to run!")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    print("🚀 Starting Cyber Tron Demo...")
    time.sleep(1)
    
    game = CyberTronDemo()
    game.main_menu()
