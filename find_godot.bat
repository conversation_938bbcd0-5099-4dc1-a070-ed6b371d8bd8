@echo off
echo ========================================
echo        Finding Godot Installation
echo ========================================
echo.

echo Checking common locations...

REM Check Desktop
if exist "%USERPROFILE%\Desktop\godot.exe" (
    echo ✅ Found Godot on Desktop: %USERPROFILE%\Desktop\godot.exe
    echo.
    echo To run Cyber Tron:
    echo 1. Double-click: %USERPROFILE%\Desktop\godot.exe
    echo 2. Click "Import" in Godot
    echo 3. Select: %~dp0project.godot
    echo 4. Press F5 to run!
    goto :found
)

REM Check Downloads
if exist "%USERPROFILE%\Downloads\godot.exe" (
    echo ✅ Found Godot in Downloads: %USERPROFILE%\Downloads\godot.exe
    echo.
    echo To run Cyber Tron:
    echo 1. Double-click: %USERPROFILE%\Downloads\godot.exe
    echo 2. Click "Import" in Godot
    echo 3. Select: %~dp0project.godot
    echo 4. Press F5 to run!
    goto :found
)

REM Check Program Files
if exist "C:\Program Files\Godot\godot.exe" (
    echo ✅ Found Godot in Program Files: C:\Program Files\Godot\godot.exe
    echo.
    echo To run Cyber Tron:
    echo 1. Double-click: C:\Program Files\Godot\godot.exe
    echo 2. Click "Import" in Godot
    echo 3. Select: %~dp0project.godot
    echo 4. Press F5 to run!
    goto :found
)

REM Check C:\Godot
if exist "C:\Godot\godot.exe" (
    echo ✅ Found Godot in C:\Godot: C:\Godot\godot.exe
    echo.
    echo To run Cyber Tron:
    echo 1. Double-click: C:\Godot\godot.exe
    echo 2. Click "Import" in Godot
    echo 3. Select: %~dp0project.godot
    echo 4. Press F5 to run!
    goto :found
)

echo ❌ Godot not found in common locations.
echo.
echo ========================================
echo        DOWNLOAD GODOT 4.x
echo ========================================
echo.
echo Please download Godot 4.x:
echo 1. Go to: https://godotengine.org/download
echo 2. Download "Godot Engine" (Standard version)
echo 3. Extract the .zip file
echo 4. Run godot.exe
echo 5. Import this project: %~dp0project.godot
echo.
echo OR search your computer for "godot.exe"
echo.
goto :end

:found
echo.
echo ========================================
echo        CYBER TRON READY!
echo ========================================
echo.
echo Your cybersecurity game is ready to play!
echo.
echo Game Features:
echo • 3D open-world environment
echo • Real cybersecurity tools (nmap, netcat, etc.)
echo • Progressive missions from beginner to expert
echo • Interactive mobile phone and PC terminals
echo • AI opponents with different skill levels
echo.

:end
pause
