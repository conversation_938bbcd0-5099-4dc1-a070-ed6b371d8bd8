@echo off
echo ========================================
echo        Cyber Tron Game Launcher
echo ========================================
echo.
echo This script will help you launch Cyber Tron
echo.

REM Check if God<PERSON> is in PATH
where godot >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Godot found in PATH. Launching game...
    godot --path "%~dp0" project.godot
    goto :end
)

REM Check common Godot installation paths
set "GODOT_PATHS=C:\Godot\godot.exe;C:\Program Files\Godot\godot.exe;C:\Program Files (x86)\Godot\godot.exe"

for %%p in (%GODOT_PATHS%) do (
    if exist "%%p" (
        echo Found Godot at: %%p
        echo Launching game...
        "%%p" --path "%~dp0" project.godot
        goto :end
    )
)

echo.
echo ========================================
echo        Godot Engine Not Found
echo ========================================
echo.
echo Please install Godot 4.x to run Cyber Tron:
echo.
echo 1. Download Godot 4.x from: https://godotengine.org/download
echo 2. Extract it to a folder (e.g., C:\Godot\)
echo 3. Add Godot to your PATH or run this script again
echo.
echo Alternative: Open Godot manually and import this project
echo by selecting the project.godot file
echo.
pause

:end
echo.
echo Game session ended.
pause
