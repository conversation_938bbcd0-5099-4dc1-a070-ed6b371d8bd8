@echo off
echo ========================================
echo        Cyber Tron Game Launcher
echo ========================================
echo.
echo This script will help you launch Cyber Tron
echo.

REM Check if <PERSON><PERSON> is in PATH
where godot >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Godot found in PATH. Launching game...
    godot --path "%~dp0" project.godot
    goto :end
)

REM Check common Godot installation paths
set "GODOT_PATHS=C:\Godot\godot.exe;C:\Program Files\Godot\godot.exe;C:\Program Files (x86)\Godot\godot.exe;%USERPROFILE%\Desktop\godot.exe;%USERPROFILE%\Downloads\godot.exe"

echo Searching for Godot installation...
for %%p in (%GODOT_PATHS%) do (
    if exist "%%p" (
        echo Found Godot at: %%p
        echo Launching Cyber Tron...
        "%%p" --path "%~dp0" project.godot
        goto :end
    )
)

REM Try to find godot.exe anywhere on the system
echo Searching entire system for Godot...
for /f "delims=" %%i in ('dir /s /b C:\godot.exe 2^>nul') do (
    echo Found Godot at: %%i
    echo Launching Cyber Tron...
    "%%i" --path "%~dp0" project.godot
    goto :end
)

echo.
echo ========================================
echo        Godot Engine Not Found
echo ========================================
echo.
echo CYBER TRON IS READY TO PLAY!
echo.
echo To launch the game:
echo.
echo METHOD 1 (Recommended):
echo 1. Open Godot 4.x (double-click godot.exe)
echo 2. Click "Import" in the project manager
echo 3. Select this folder: %~dp0
echo 4. Choose project.godot file
echo 5. Click "Import & Edit"
echo 6. Press F5 to run the game!
echo.
echo METHOD 2:
echo 1. Download Godot 4.x from: https://godotengine.org/download
echo 2. Extract godot.exe to C:\Godot\
echo 3. Run this script again
echo.
echo The game includes:
echo - 3D open-world cybersecurity training
echo - Real cybersecurity tools (nmap, netcat, etc.)
echo - Progressive missions from beginner to expert
echo - AI opponents and interactive terminals
echo.
pause

:end
echo.
echo Thanks for playing Cyber Tron!
echo Check README.md for more information.
pause
