[gd_scene load_steps=8 format=3 uid="uid://bwxq8q8q8q8q8"]

[ext_resource type="Script" path="res://scripts/scenes/main_world.gd" id="1_1a2b3"]
[ext_resource type="Script" path="res://scripts/player/player_controller.gd" id="2_4d5e6"]
[ext_resource type="Script" path="res://scripts/core/game_manager.gd" id="3_7g8h9"]
[ext_resource type="Script" path="res://scripts/core/mission_manager.gd" id="4_0i1j2"]
[ext_resource type="Script" path="res://scripts/core/cyber_tools_manager.gd" id="5_3k4l5"]
[ext_resource type="Script" path="res://scripts/ui/mobile_interface.gd" id="6_6m7n8"]
[ext_resource type="Script" path="res://scripts/ui/pc_terminal.gd" id="7_9o0p1"]

[node name="Main" type="Node3D"]
script = ExtResource("1_1a2b3")

[node name="Environment" type="Node3D" parent="."]

[node name="Ground" type="StaticBody3D" parent="Environment"]

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/Ground"]
transform = Transform3D(50, 0, 0, 0, 1, 0, 0, 0, 50, 0, -1, 0)
mesh = SubResource("BoxMesh_1a2b3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/Ground"]
transform = Transform3D(50, 0, 0, 0, 1, 0, 0, 0, 50, 0, -1, 0)
shape = SubResource("BoxShape3D_4d5e6")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 1.0
shadow_enabled = true

[node name="Player" type="CharacterBody3D" parent="."]
script = ExtResource("2_4d5e6")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
mesh = SubResource("CapsuleMesh_7g8h9")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
shape = SubResource("CapsuleShape3D_0i1j2")

[node name="CameraPivot" type="Node3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.5, 0)

[node name="Camera3D" type="Camera3D" parent="Player/CameraPivot"]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 2, 5)

[node name="InteractionArea" type="Area3D" parent="Player"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player/InteractionArea"]
shape = SubResource("SphereShape3D_3k4l5")

[node name="UI" type="Control" parent="Player"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MobileInterface" type="Control" parent="Player/UI"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("6_6m7n8")

[node name="PCTerminal" type="Control" parent="Player/UI"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("7_9o0p1")

[node name="GameManager" type="Node" parent="."]
script = ExtResource("3_7g8h9")

[node name="MissionManager" type="Node" parent="GameManager"]
script = ExtResource("4_0i1j2")

[node name="CyberToolsManager" type="Node" parent="GameManager"]
script = ExtResource("5_3k4l5")

[node name="UIManager" type="Node" parent="GameManager"]

[node name="Interactables" type="Node3D" parent="."]

[node name="AIOpponents" type="Node3D" parent="."]

[node name="UILayer" type="CanvasLayer" parent="."]

[node name="MobileInterface" type="Control" parent="UILayer"]
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("6_6m7n8")

[node name="PCTerminal" type="Control" parent="UILayer"]
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("7_9o0p1")

[node name="HUD" type="Control" parent="UILayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MissionNotification" type="Control" parent="UILayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
