extends CharacterBody3D
class_name AIOpponent

# AI Opponent system for Cyber Tron
# Represents cybersecurity adversaries that players must defend against or compete with

@export var opponent_name: String = "Cyber Adversary"
@export var difficulty_level: int = 1
@export var movement_speed: float = 3.0
@export var detection_range: float = 10.0
@export var attack_range: float = 5.0

@onready var mesh: MeshInstance3D = $MeshInstance3D
@onready var detection_area: Area3D = $DetectionArea
@onready var attack_timer: Timer = $AttackTimer
@onready var navigation_agent: NavigationAgent3D = $NavigationAgent3D

enum AIState {
	IDLE,
	PATROLLING,
	INVESTIGATING,
	ATTACKING,
	RETREATING,
	HACKING
}

var current_state: AIState = AIState.IDLE
var target_player: Node3D = null
var patrol_points: Array = []
var current_patrol_index: int = 0
var last_known_player_position: Vector3
var gravity = ProjectSettings.get_setting("physics/3d/default_gravity")

# Cybersecurity-specific AI properties
var hacking_skills: Dictionary = {
	"network_scanning": 1,
	"exploitation": 1,
	"social_engineering": 1,
	"persistence": 1
}
var current_attack_type: String = ""
var attack_progress: float = 0.0
var is_performing_cyber_attack: bool = false

signal opponent_detected_player(opponent: AIOpponent, player: Node3D)
signal cyber_attack_started(attack_type: String, opponent: AIOpponent)
signal cyber_attack_completed(attack_type: String, success: bool, opponent: AIOpponent)

func _ready():
	print("AI Opponent initialized: ", opponent_name)
	setup_opponent()
	initialize_patrol_points()
	
	# Connect signals
	detection_area.body_entered.connect(_on_detection_area_entered)
	detection_area.body_exited.connect(_on_detection_area_exited)
	attack_timer.timeout.connect(_on_attack_timer_timeout)
	
	# Set initial state
	change_state(AIState.PATROLLING)

func setup_opponent():
	"""Setup opponent based on difficulty level"""
	match difficulty_level:
		1:
			setup_beginner_opponent()
		2:
			setup_intermediate_opponent()
		3:
			setup_advanced_opponent()
		4:
			setup_expert_opponent()
		_:
			setup_expert_opponent()

func setup_beginner_opponent():
	"""Setup beginner level opponent"""
	opponent_name = "Script Kiddie"
	hacking_skills = {
		"network_scanning": 2,
		"exploitation": 1,
		"social_engineering": 1,
		"persistence": 1
	}
	movement_speed = 2.0
	detection_range = 8.0

func setup_intermediate_opponent():
	"""Setup intermediate level opponent"""
	opponent_name = "Cyber Criminal"
	hacking_skills = {
		"network_scanning": 4,
		"exploitation": 3,
		"social_engineering": 3,
		"persistence": 2
	}
	movement_speed = 3.0
	detection_range = 12.0

func setup_advanced_opponent():
	"""Setup advanced level opponent"""
	opponent_name = "APT Actor"
	hacking_skills = {
		"network_scanning": 6,
		"exploitation": 5,
		"social_engineering": 5,
		"persistence": 4
	}
	movement_speed = 4.0
	detection_range = 15.0

func setup_expert_opponent():
	"""Setup expert level opponent"""
	opponent_name = "Nation State Actor"
	hacking_skills = {
		"network_scanning": 8,
		"exploitation": 7,
		"social_engineering": 7,
		"persistence": 6
	}
	movement_speed = 5.0
	detection_range = 20.0

func initialize_patrol_points():
	"""Initialize patrol points for the AI"""
	# Default patrol points - can be overridden by level design
	patrol_points = [
		global_position + Vector3(10, 0, 0),
		global_position + Vector3(10, 0, 10),
		global_position + Vector3(-10, 0, 10),
		global_position + Vector3(-10, 0, -10),
		global_position
	]

func _physics_process(delta):
	# Handle gravity
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	# Handle AI behavior based on current state
	match current_state:
		AIState.IDLE:
			handle_idle_state(delta)
		AIState.PATROLLING:
			handle_patrol_state(delta)
		AIState.INVESTIGATING:
			handle_investigate_state(delta)
		AIState.ATTACKING:
			handle_attack_state(delta)
		AIState.RETREATING:
			handle_retreat_state(delta)
		AIState.HACKING:
			handle_hacking_state(delta)
	
	move_and_slide()

func handle_idle_state(delta: float):
	"""Handle idle state behavior"""
	velocity.x = 0
	velocity.z = 0
	
	# Transition to patrolling after a short delay
	await get_tree().create_timer(2.0).timeout
	change_state(AIState.PATROLLING)

func handle_patrol_state(delta: float):
	"""Handle patrolling behavior"""
	if patrol_points.is_empty():
		return
	
	var target_point = patrol_points[current_patrol_index]
	navigation_agent.target_position = target_point
	
	var direction = navigation_agent.get_next_path_position() - global_position
	direction = direction.normalized()
	
	velocity.x = direction.x * movement_speed
	velocity.z = direction.z * movement_speed
	
	# Check if reached patrol point
	if global_position.distance_to(target_point) < 2.0:
		current_patrol_index = (current_patrol_index + 1) % patrol_points.size()
		await get_tree().create_timer(1.0).timeout  # Pause at patrol point

func handle_investigate_state(delta: float):
	"""Handle investigation behavior"""
	if last_known_player_position != Vector3.ZERO:
		navigation_agent.target_position = last_known_player_position
		
		var direction = navigation_agent.get_next_path_position() - global_position
		direction = direction.normalized()
		
		velocity.x = direction.x * movement_speed * 1.5  # Move faster when investigating
		velocity.z = direction.z * movement_speed * 1.5
		
		# If reached investigation point and no player found, return to patrol
		if global_position.distance_to(last_known_player_position) < 2.0:
			await get_tree().create_timer(3.0).timeout  # Look around
			change_state(AIState.PATROLLING)

func handle_attack_state(delta: float):
	"""Handle attack behavior"""
	if target_player:
		# Move towards player
		navigation_agent.target_position = target_player.global_position
		
		var direction = navigation_agent.get_next_path_position() - global_position
		direction = direction.normalized()
		
		velocity.x = direction.x * movement_speed
		velocity.z = direction.z * movement_speed
		
		# Check if in attack range
		var distance_to_player = global_position.distance_to(target_player.global_position)
		if distance_to_player <= attack_range:
			start_cyber_attack()
	else:
		change_state(AIState.PATROLLING)

func handle_retreat_state(delta: float):
	"""Handle retreat behavior"""
	if target_player:
		# Move away from player
		var direction = (global_position - target_player.global_position).normalized()
		velocity.x = direction.x * movement_speed * 1.5
		velocity.z = direction.z * movement_speed * 1.5
		
		# Retreat for a certain distance then return to patrol
		var distance_to_player = global_position.distance_to(target_player.global_position)
		if distance_to_player > detection_range * 2:
			change_state(AIState.PATROLLING)

func handle_hacking_state(delta: float):
	"""Handle hacking behavior"""
	velocity.x = 0
	velocity.z = 0
	
	# Simulate hacking progress
	if is_performing_cyber_attack:
		attack_progress += delta * (hacking_skills.get(current_attack_type, 1) / 10.0)
		
		if attack_progress >= 1.0:
			complete_cyber_attack(true)

func change_state(new_state: AIState):
	"""Change AI state"""
	current_state = new_state
	print(opponent_name, " changed state to: ", AIState.keys()[new_state])

func start_cyber_attack():
	"""Start a cybersecurity attack"""
	if is_performing_cyber_attack:
		return
	
	# Choose attack type based on skills and situation
	var possible_attacks = ["network_scanning", "exploitation", "social_engineering", "persistence"]
	current_attack_type = possible_attacks[randi() % possible_attacks.size()]
	
	is_performing_cyber_attack = true
	attack_progress = 0.0
	change_state(AIState.HACKING)
	
	cyber_attack_started.emit(current_attack_type, self)
	print(opponent_name, " started ", current_attack_type, " attack!")

func complete_cyber_attack(success: bool):
	"""Complete the current cyber attack"""
	is_performing_cyber_attack = false
	attack_progress = 0.0
	
	cyber_attack_completed.emit(current_attack_type, success, self)
	
	if success:
		print(opponent_name, " successfully completed ", current_attack_type, " attack!")
		# Could trigger mission failure or defensive challenges here
	else:
		print(opponent_name, " failed ", current_attack_type, " attack!")
	
	current_attack_type = ""
	change_state(AIState.RETREATING)

func _on_detection_area_entered(body):
	"""Handle player detection"""
	if body.is_in_group("player"):
		target_player = body
		last_known_player_position = body.global_position
		opponent_detected_player.emit(self, body)
		
		# Decide on action based on difficulty and situation
		if difficulty_level >= 3:
			change_state(AIState.ATTACKING)
		else:
			change_state(AIState.INVESTIGATING)

func _on_detection_area_exited(body):
	"""Handle player leaving detection area"""
	if body == target_player:
		last_known_player_position = target_player.global_position
		target_player = null
		change_state(AIState.INVESTIGATING)

func _on_attack_timer_timeout():
	"""Handle attack timer timeout"""
	if current_state == AIState.HACKING:
		complete_cyber_attack(false)  # Attack failed due to timeout

func set_patrol_points(points: Array):
	"""Set custom patrol points"""
	patrol_points = points
	current_patrol_index = 0

func get_opponent_info() -> Dictionary:
	"""Get opponent information"""
	return {
		"name": opponent_name,
		"difficulty": difficulty_level,
		"state": AIState.keys()[current_state],
		"skills": hacking_skills,
		"current_attack": current_attack_type,
		"attack_progress": attack_progress
	}
