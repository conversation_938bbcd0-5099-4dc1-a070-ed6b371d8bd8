extends Node
class_name <PERSON>Manager

# Main game manager for Cyber Tron
# Handles overall game state, progression, and coordination between systems

@export var starting_level: int = 1
@export var max_level: int = 10

@onready var mission_manager: MissionManager = $MissionManager
@onready var cyber_tools_manager: CyberToolsManager = $CyberToolsManager

var current_level: int = 1
var player_score: int = 0
var completed_missions: Array = []
var player_skills: Dictionary = {
	"networking": 0,
	"penetration_testing": 0,
	"social_engineering": 0,
	"cryptography": 0,
	"forensics": 0
}

signal level_up(new_level: int)
signal skill_improved(skill_name: String, new_level: int)
signal game_state_changed(state: String)

func _ready():
	print("Cyber Tron Game Manager initialized")
	initialize_game()

func initialize_game():
	"""Initialize the game with starting parameters"""
	current_level = starting_level
	load_level_missions()
	
	# Connect to mission manager signals
	if mission_manager:
		mission_manager.mission_completed.connect(_on_mission_completed)
		mission_manager.mission_failed.connect(_on_mission_failed)

func load_level_missions():
	"""Load missions appropriate for current level"""
	if mission_manager:
		mission_manager.load_missions_for_level(current_level)

func _on_mission_completed(mission_data: Dictionary):
	"""Handle mission completion"""
	completed_missions.append(mission_data)
	player_score += mission_data.get("score_reward", 100)
	
	# Improve relevant skills
	var skills_gained = mission_data.get("skills_gained", {})
	for skill in skills_gained:
		improve_skill(skill, skills_gained[skill])
	
	# Check for level up
	check_level_progression()
	
	print("Mission completed: ", mission_data.title)

func _on_mission_failed(mission_data: Dictionary):
	"""Handle mission failure"""
	print("Mission failed: ", mission_data.title)
	# Could implement retry logic or penalties here

func improve_skill(skill_name: String, points: int):
	"""Improve a player skill"""
	if skill_name in player_skills:
		player_skills[skill_name] += points
		skill_improved.emit(skill_name, player_skills[skill_name])
		print("Skill improved: ", skill_name, " -> ", player_skills[skill_name])

func check_level_progression():
	"""Check if player should level up"""
	var missions_needed = current_level * 2  # 2 missions per level
	
	if completed_missions.size() >= missions_needed and current_level < max_level:
		level_up_player()

func level_up_player():
	"""Level up the player"""
	current_level += 1
	level_up.emit(current_level)
	load_level_missions()
	
	print("Level up! Now level: ", current_level)
	
	# Unlock new tools and missions
	if cyber_tools_manager:
		cyber_tools_manager.unlock_tools_for_level(current_level)

func get_player_stats() -> Dictionary:
	"""Get current player statistics"""
	return {
		"level": current_level,
		"score": player_score,
		"completed_missions": completed_missions.size(),
		"skills": player_skills
	}

func save_game():
	"""Save game progress"""
	var save_data = {
		"level": current_level,
		"score": player_score,
		"completed_missions": completed_missions,
		"skills": player_skills
	}
	
	var save_file = FileAccess.open("user://cyber_tron_save.dat", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()
		print("Game saved successfully")

func load_game():
	"""Load game progress"""
	var save_file = FileAccess.open("user://cyber_tron_save.dat", FileAccess.READ)
	if save_file:
		var save_data = JSON.parse_string(save_file.get_as_text())
		save_file.close()
		
		if save_data:
			current_level = save_data.get("level", 1)
			player_score = save_data.get("score", 0)
			completed_missions = save_data.get("completed_missions", [])
			player_skills = save_data.get("skills", player_skills)
			
			load_level_missions()
			print("Game loaded successfully")
			return true
	
	print("No save file found or failed to load")
	return false
