extends Node
class_name MissionManager

# Mission management system for Cyber Tron
# Handles mission creation, progression, and completion tracking

var available_missions: Array = []
var active_missions: Array = []
var completed_missions: Array = []

signal mission_completed(mission_data: Dictionary)
signal mission_failed(mission_data: Dictionary)
signal new_mission_available(mission_data: Dictionary)

func _ready():
	print("Mission Manager initialized")

func load_missions_for_level(level: int):
	"""Load appropriate missions for the given level"""
	available_missions.clear()
	
	match level:
		1:
			load_beginner_missions()
		2:
			load_intermediate_missions()
		3:
			load_advanced_missions()
		_:
			load_expert_missions()

func load_beginner_missions():
	"""Load beginner level missions"""
	var missions = [
		{
			"id": "beginner_001",
			"title": "First Network Scan",
			"description": "Learn to use nmap to discover devices on your network. This is your first step into cybersecurity!",
			"difficulty": "Beginner",
			"level_required": 1,
			"tools_required": ["nmap"],
			"objectives": [
				"Open the terminal on the PC",
				"Run: nmap -sn ***********/24",
				"Identify at least 3 active hosts",
				"Document the results"
			],
			"score_reward": 100,
			"skills_gained": {"networking": 1},
			"status": "available",
			"estimated_time": "10 minutes",
			"hints": [
				"The -sn flag performs a ping scan",
				"Look for hosts that respond to ping",
				"Note down IP addresses that are active"
			]
		},
		{
			"id": "beginner_002", 
			"title": "Port Discovery",
			"description": "Discover what services are running on network devices by scanning their ports.",
			"difficulty": "Beginner",
			"level_required": 1,
			"tools_required": ["nmap"],
			"objectives": [
				"Choose a target IP from previous scan",
				"Run: nmap -sS [target_ip]",
				"Identify open ports",
				"Research what services run on those ports"
			],
			"score_reward": 150,
			"skills_gained": {"networking": 2, "penetration_testing": 1},
			"status": "available",
			"estimated_time": "15 minutes",
			"hints": [
				"Common ports: 22 (SSH), 80 (HTTP), 443 (HTTPS)",
				"Use -sS for SYN scan (stealth scan)",
				"Document all open ports found"
			]
		},
		{
			"id": "beginner_003",
			"title": "Service Identification", 
			"description": "Learn to identify specific services and their versions running on open ports.",
			"difficulty": "Beginner",
			"level_required": 1,
			"tools_required": ["nmap"],
			"objectives": [
				"Run: nmap -sV [target_ip]",
				"Identify service versions",
				"Look for potential vulnerabilities",
				"Create a basic report"
			],
			"score_reward": 200,
			"skills_gained": {"networking": 2, "penetration_testing": 2},
			"status": "available",
			"estimated_time": "20 minutes",
			"hints": [
				"The -sV flag enables version detection",
				"Pay attention to outdated software versions",
				"Note any unusual or unexpected services"
			]
		}
	]
	
	for mission in missions:
		available_missions.append(mission)
		new_mission_available.emit(mission)

func load_intermediate_missions():
	"""Load intermediate level missions"""
	var missions = [
		{
			"id": "intermediate_001",
			"title": "Vulnerability Scanning",
			"description": "Use advanced nmap scripts to identify potential security vulnerabilities.",
			"difficulty": "Intermediate", 
			"level_required": 2,
			"tools_required": ["nmap", "nmap-scripts"],
			"objectives": [
				"Run: nmap --script vuln [target_ip]",
				"Analyze vulnerability scan results",
				"Prioritize findings by severity",
				"Create vulnerability report"
			],
			"score_reward": 300,
			"skills_gained": {"penetration_testing": 3, "networking": 1},
			"status": "available",
			"estimated_time": "30 minutes"
		},
		{
			"id": "intermediate_002",
			"title": "Password Attack Simulation",
			"description": "Learn ethical password testing using dictionary attacks.",
			"difficulty": "Intermediate",
			"level_required": 2, 
			"tools_required": ["hydra", "wordlists"],
			"objectives": [
				"Set up a test environment",
				"Use hydra for SSH brute force",
				"Test common passwords",
				"Document weak credentials found"
			],
			"score_reward": 400,
			"skills_gained": {"penetration_testing": 4, "cryptography": 2},
			"status": "available",
			"estimated_time": "45 minutes"
		}
	]
	
	for mission in missions:
		available_missions.append(mission)
		new_mission_available.emit(mission)

func load_advanced_missions():
	"""Load advanced level missions"""
	var missions = [
		{
			"id": "advanced_001",
			"title": "Web Application Testing",
			"description": "Perform comprehensive web application security testing.",
			"difficulty": "Advanced",
			"level_required": 3,
			"tools_required": ["burp_suite", "sqlmap", "nikto"],
			"objectives": [
				"Set up proxy for web traffic analysis",
				"Identify SQL injection vulnerabilities", 
				"Test for XSS vulnerabilities",
				"Generate comprehensive security report"
			],
			"score_reward": 600,
			"skills_gained": {"penetration_testing": 5, "networking": 2},
			"status": "available",
			"estimated_time": "60 minutes"
		}
	]
	
	for mission in missions:
		available_missions.append(mission)
		new_mission_available.emit(mission)

func load_expert_missions():
	"""Load expert level missions"""
	var missions = [
		{
			"id": "expert_001",
			"title": "Advanced Persistent Threat Simulation",
			"description": "Simulate a complete APT attack chain from reconnaissance to data exfiltration.",
			"difficulty": "Expert",
			"level_required": 4,
			"tools_required": ["metasploit", "cobalt_strike", "wireshark"],
			"objectives": [
				"Conduct advanced reconnaissance",
				"Exploit multiple vulnerabilities",
				"Establish persistent access",
				"Simulate data exfiltration",
				"Create detailed attack timeline"
			],
			"score_reward": 1000,
			"skills_gained": {"penetration_testing": 8, "forensics": 5, "networking": 3},
			"status": "available",
			"estimated_time": "120 minutes"
		}
	]
	
	for mission in missions:
		available_missions.append(mission)
		new_mission_available.emit(mission)

func start_mission(mission_id: String) -> bool:
	"""Start a mission by ID"""
	for mission in available_missions:
		if mission.id == mission_id:
			active_missions.append(mission)
			mission.status = "active"
			print("Mission started: ", mission.title)
			return true
	return false

func complete_mission(mission_id: String, success: bool = true):
	"""Complete a mission"""
	for i in range(active_missions.size()):
		if active_missions[i].id == mission_id:
			var mission = active_missions[i]
			active_missions.remove_at(i)
			
			if success:
				mission.status = "completed"
				completed_missions.append(mission)
				mission_completed.emit(mission)
			else:
				mission.status = "failed"
				mission_failed.emit(mission)
			break

func get_available_missions() -> Array:
	"""Get all available missions"""
	return available_missions

func get_active_missions() -> Array:
	"""Get all active missions"""
	return active_missions

func get_mission_by_id(mission_id: String) -> Dictionary:
	"""Get a specific mission by ID"""
	for mission in available_missions + active_missions + completed_missions:
		if mission.id == mission_id:
			return mission
	return {}
