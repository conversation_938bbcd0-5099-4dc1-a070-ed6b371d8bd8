extends CharacterBody3D
class_name <PERSON><PERSON>ontroller

# Player movement and interaction controller for Cyber Tron
# GTA-style third-person movement with cybersecurity game interactions

@export var speed: float = 5.0
@export var jump_velocity: float = 4.5
@export var mouse_sensitivity: float = 0.002
@export var camera_distance: float = 5.0

@onready var camera_pivot: Node3D = $CameraPivot
@onready var camera: Camera3D = $CameraPivot/Camera3D
@onready var mesh: MeshInstance3D = $MeshInstance3D
@onready var interaction_area: Area3D = $InteractionArea
@onready var mobile_ui: Control = $UI/MobileInterface
@onready var pc_terminal_ui: Control = $UI/PCTerminal

var gravity = ProjectSettings.get_setting("physics/3d/default_gravity")
var current_interactable = null
var is_mobile_open: bool = false
var is_at_pc: bool = false

signal mission_received(mission_data)
signal pc_accessed()

func _ready():
	# Setup camera position
	camera.position = Vector3(0, 2, camera_distance)
	camera.look_at(Vector3.ZERO, Vector3.UP)
	
	# Connect interaction signals
	interaction_area.body_entered.connect(_on_interactable_entered)
	interaction_area.body_exited.connect(_on_interactable_exited)
	
	# Hide UI elements initially
	mobile_ui.visible = false
	pc_terminal_ui.visible = false
	
	# Capture mouse for camera control
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _input(event):
	# Handle mouse look
	if event is InputEventMouseMotion and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		rotate_y(-event.relative.x * mouse_sensitivity)
		camera_pivot.rotate_x(-event.relative.y * mouse_sensitivity)
		camera_pivot.rotation.x = clamp(camera_pivot.rotation.x, -PI/3, PI/3)
	
	# Handle mobile phone toggle
	if Input.is_action_just_pressed("open_mobile"):
		toggle_mobile()
	
	# Handle interaction
	if Input.is_action_just_pressed("interact"):
		if current_interactable:
			interact_with_object()

func _physics_process(delta):
	# Handle gravity
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	# Handle jump
	if Input.is_action_just_pressed("jump") and is_on_floor():
		velocity.y = jump_velocity
	
	# Handle movement
	var input_dir = Vector2.ZERO
	if not is_mobile_open and not is_at_pc:
		input_dir = Input.get_vector("move_left", "move_right", "move_forward", "move_backward")
	
	if input_dir != Vector2.ZERO:
		# Move relative to player's rotation
		var direction = (transform.basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()
		velocity.x = direction.x * speed
		velocity.z = direction.z * speed
	else:
		velocity.x = move_toward(velocity.x, 0, speed)
		velocity.z = move_toward(velocity.z, 0, speed)
	
	move_and_slide()

func toggle_mobile():
	is_mobile_open = !is_mobile_open
	mobile_ui.visible = is_mobile_open
	
	if is_mobile_open:
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	else:
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func interact_with_object():
	if current_interactable.has_method("interact"):
		current_interactable.interact()
		
		# Check if it's a PC
		if current_interactable.is_in_group("computers"):
			access_pc()

func access_pc():
	is_at_pc = true
	pc_terminal_ui.visible = true
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	pc_accessed.emit()

func exit_pc():
	is_at_pc = false
	pc_terminal_ui.visible = false
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func receive_mission(mission_data):
	mission_received.emit(mission_data)
	# Show notification or update mobile interface

func _on_interactable_entered(body):
	if body.is_in_group("interactables"):
		current_interactable = body
		# Show interaction prompt
		print("Press E to interact with ", body.name)

func _on_interactable_exited(body):
	if body == current_interactable:
		current_interactable = null
		# Hide interaction prompt
