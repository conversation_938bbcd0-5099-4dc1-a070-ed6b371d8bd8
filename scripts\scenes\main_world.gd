extends Node3D
class_name MainWorld

# Main world scene for Cyber Tron
# Manages the game world, player, NPCs, and interactive objects

@onready var player: PlayerController = $Player
@onready var game_manager: GameManager = $GameManager
@onready var ui_layer: CanvasLayer = $UILayer
@onready var environment: Node3D = $Environment
@onready var interactables: Node3D = $Interactables
@onready var ai_opponents: Node3D = $AIOpponents

# UI References
@onready var mobile_interface: MobileInterface = $UILayer/MobileInterface
@onready var pc_terminal: PCTerminal = $UILayer/PCTerminal
@onready var hud: Control = $UILayer/HUD
@onready var mission_notification: Control = $UILayer/MissionNotification

var current_level: int = 1
var active_computers: Array = []
var active_opponents: Array = []

signal world_loaded()
signal level_changed(new_level: int)

func _ready():
	print("Main World initializing...")
	setup_world()
	connect_signals()
	spawn_initial_objects()
	
	# Initialize game systems
	if game_manager:
		game_manager.initialize_game()
	
	world_loaded.emit()
	print("Main World loaded successfully!")

func setup_world():
	"""Setup the main world environment"""
	# Hide UI elements initially
	if mobile_interface:
		mobile_interface.visible = false
	if pc_terminal:
		pc_terminal.visible = false
	
	# Setup player
	if player:
		player.global_position = Vector3(0, 1, 0)
		# Add player to group for AI detection
		player.add_to_group("player")

func connect_signals():
	"""Connect all necessary signals"""
	if player and mobile_interface:
		player.mission_received.connect(_on_mission_received)
		player.pc_accessed.connect(_on_pc_accessed)
		mobile_interface.mission_selected.connect(_on_mission_selected)
		mobile_interface.mobile_closed.connect(_on_mobile_closed)
	
	if pc_terminal:
		pc_terminal.terminal_closed.connect(_on_terminal_closed)
		pc_terminal.command_executed.connect(_on_command_executed)
	
	if game_manager:
		game_manager.level_up.connect(_on_level_up)
		game_manager.mission_manager.mission_completed.connect(_on_mission_completed)
		game_manager.mission_manager.new_mission_available.connect(_on_new_mission_available)
	
	# Connect cyber tools manager to PC terminal
	if game_manager and pc_terminal:
		pc_terminal.set_cyber_tools_manager(game_manager.cyber_tools_manager)

func spawn_initial_objects():
	"""Spawn initial interactive objects and NPCs"""
	spawn_computers()
	spawn_ai_opponents()
	create_sample_missions()

func spawn_computers():
	"""Spawn interactive computers in the world"""
	var computer_positions = [
		Vector3(5, 0, 5),
		Vector3(-5, 0, 5),
		Vector3(10, 0, -5),
		Vector3(-10, 0, -5)
	]
	
	for i in range(computer_positions.size()):
		var computer = create_computer(computer_positions[i], "Computer_" + str(i + 1))
		active_computers.append(computer)

func create_computer(position: Vector3, computer_name: String) -> StaticBody3D:
	"""Create an interactive computer"""
	var computer = StaticBody3D.new()
	computer.name = computer_name
	computer.global_position = position
	
	# Add visual representation
	var mesh_instance = MeshInstance3D.new()
	var box_mesh = BoxMesh.new()
	box_mesh.size = Vector3(1, 1.5, 0.5)
	mesh_instance.mesh = box_mesh
	computer.add_child(mesh_instance)
	
	# Add collision
	var collision_shape = CollisionShape3D.new()
	var box_shape = BoxShape3D.new()
	box_shape.size = Vector3(1, 1.5, 0.5)
	collision_shape.shape = box_shape
	computer.add_child(collision_shape)
	
	# Add interaction script
	var interaction_script = GDScript.new()
	interaction_script.source_code = """
extends StaticBody3D

func interact():
	print('Accessing computer: ', name)
	# This will be handled by the player controller
	pass
"""
	computer.set_script(interaction_script)
	
	# Add to groups
	computer.add_to_group("interactables")
	computer.add_to_group("computers")
	
	interactables.add_child(computer)
	return computer

func spawn_ai_opponents():
	"""Spawn AI opponents in the world"""
	var opponent_positions = [
		Vector3(15, 0, 15),
		Vector3(-15, 0, 15),
		Vector3(15, 0, -15),
		Vector3(-15, 0, -15)
	]
	
	for i in range(opponent_positions.size()):
		var opponent = create_ai_opponent(opponent_positions[i], current_level)
		active_opponents.append(opponent)

func create_ai_opponent(position: Vector3, difficulty: int) -> AIOpponent:
	"""Create an AI opponent"""
	var opponent_scene = preload("res://scripts/ai/ai_opponent.gd")
	var opponent = AIOpponent.new()
	opponent.global_position = position
	opponent.difficulty_level = difficulty
	
	# Add visual representation
	var mesh_instance = MeshInstance3D.new()
	var capsule_mesh = CapsuleMesh.new()
	capsule_mesh.height = 2.0
	capsule_mesh.top_radius = 0.3
	capsule_mesh.bottom_radius = 0.3
	mesh_instance.mesh = capsule_mesh
	opponent.add_child(mesh_instance)
	
	# Add collision
	var collision_shape = CollisionShape3D.new()
	var capsule_shape = CapsuleShape3D.new()
	capsule_shape.height = 2.0
	capsule_shape.radius = 0.3
	collision_shape.shape = capsule_shape
	opponent.add_child(collision_shape)
	
	# Add detection area
	var detection_area = Area3D.new()
	var detection_collision = CollisionShape3D.new()
	var detection_shape = SphereShape3D.new()
	detection_shape.radius = opponent.detection_range
	detection_collision.shape = detection_shape
	detection_area.add_child(detection_collision)
	opponent.add_child(detection_area)
	
	# Connect opponent signals
	opponent.opponent_detected_player.connect(_on_opponent_detected_player)
	opponent.cyber_attack_started.connect(_on_cyber_attack_started)
	opponent.cyber_attack_completed.connect(_on_cyber_attack_completed)
	
	ai_opponents.add_child(opponent)
	return opponent

func create_sample_missions():
	"""Create sample missions for testing"""
	if mobile_interface:
		mobile_interface.create_sample_missions()

func _on_mission_received(mission_data: Dictionary):
	"""Handle mission received by player"""
	print("Mission received: ", mission_data.get("title", "Unknown"))
	if mobile_interface:
		mobile_interface.add_mission(mission_data)

func _on_mission_selected(mission_data: Dictionary):
	"""Handle mission selection"""
	print("Mission selected: ", mission_data.title)
	if game_manager and game_manager.mission_manager:
		game_manager.mission_manager.start_mission(mission_data.id)
	
	# Show mission notification
	show_mission_notification("Mission Started: " + mission_data.title)

func _on_mobile_closed():
	"""Handle mobile interface closed"""
	if player:
		player.toggle_mobile()

func _on_pc_accessed():
	"""Handle PC access"""
	print("PC accessed by player")
	if pc_terminal:
		pc_terminal.visible = true

func _on_terminal_closed():
	"""Handle terminal closed"""
	if pc_terminal:
		pc_terminal.visible = false
	if player:
		player.exit_pc()

func _on_command_executed(command: String, result: Dictionary):
	"""Handle command execution in terminal"""
	print("Command executed: ", command)
	
	# Check if command completes mission objectives
	check_mission_progress(command, result)

func _on_level_up(new_level: int):
	"""Handle player level up"""
	current_level = new_level
	level_changed.emit(new_level)
	
	# Spawn more challenging opponents
	spawn_level_opponents(new_level)
	
	show_mission_notification("Level Up! Now Level " + str(new_level))

func _on_mission_completed(mission_data: Dictionary):
	"""Handle mission completion"""
	show_mission_notification("Mission Completed: " + mission_data.title)
	
	# Award experience or unlock new areas
	print("Mission completed: ", mission_data.title)

func _on_new_mission_available(mission_data: Dictionary):
	"""Handle new mission availability"""
	if mobile_interface:
		mobile_interface.add_mission(mission_data)

func _on_opponent_detected_player(opponent: AIOpponent, player_node: Node3D):
	"""Handle AI opponent detecting player"""
	print("Player detected by: ", opponent.opponent_name)
	show_mission_notification("Warning: " + opponent.opponent_name + " detected you!")

func _on_cyber_attack_started(attack_type: String, opponent: AIOpponent):
	"""Handle cyber attack started by AI"""
	print("Cyber attack started: ", attack_type, " by ", opponent.opponent_name)
	show_mission_notification("Cyber Attack: " + attack_type + " by " + opponent.opponent_name)

func _on_cyber_attack_completed(attack_type: String, success: bool, opponent: AIOpponent):
	"""Handle cyber attack completion"""
	if success:
		print("Cyber attack succeeded: ", attack_type)
		show_mission_notification("Attack Successful: " + attack_type + " - Defend yourself!")
		# Could trigger defensive missions or penalties
	else:
		print("Cyber attack failed: ", attack_type)
		show_mission_notification("Attack Failed: " + attack_type + " - Well defended!")

func spawn_level_opponents(level: int):
	"""Spawn opponents appropriate for the level"""
	var new_opponent_count = level
	for i in range(new_opponent_count):
		var random_position = Vector3(
			randf_range(-20, 20),
			0,
			randf_range(-20, 20)
		)
		var opponent = create_ai_opponent(random_position, level)
		active_opponents.append(opponent)

func check_mission_progress(command: String, result: Dictionary):
	"""Check if executed command progresses any active missions"""
	if not game_manager or not game_manager.mission_manager:
		return
	
	var active_missions = game_manager.mission_manager.get_active_missions()
	for mission in active_missions:
		# Simple objective checking - could be more sophisticated
		for objective in mission.get("objectives", []):
			if command.to_lower() in objective.to_lower():
				print("Mission objective potentially completed: ", objective)
				# Could implement more detailed objective tracking here

func show_mission_notification(message: String):
	"""Show a mission notification to the player"""
	print("Notification: ", message)
	# Could implement a proper notification UI here

func get_world_stats() -> Dictionary:
	"""Get current world statistics"""
	return {
		"level": current_level,
		"computers": active_computers.size(),
		"opponents": active_opponents.size(),
		"player_position": player.global_position if player else Vector3.ZERO
	}
