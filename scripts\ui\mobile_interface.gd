extends Control
class_name MobileInterface

# Mobile phone interface for receiving missions and game information
# Simulates a smartphone within the cybersecurity game

@onready var mission_list: VBoxContainer = $Background/VBox/MissionList
@onready var current_mission_label: Label = $Background/VBox/CurrentMission
@onready var notification_panel: Panel = $NotificationPanel
@onready var notification_label: Label = $NotificationPanel/Label
@onready var close_button: Button = $Background/VBox/CloseButton

var current_missions: Array = []
var active_mission = null

signal mission_selected(mission_data)
signal mobile_closed()

func _ready():
	close_button.pressed.connect(_on_close_pressed)
	
	# Initialize with welcome message
	show_notification("Welcome to Cyber Tron! Check your missions.")
	update_mission_display()

func add_mission(mission_data: Dictionary):
	"""Add a new mission to the mobile interface"""
	current_missions.append(mission_data)
	create_mission_button(mission_data)
	show_notification("New Mission: " + mission_data.title)

func create_mission_button(mission_data: Dictionary):
	"""Create a clickable mission button"""
	var mission_button = Button.new()
	mission_button.text = mission_data.title + "\nDifficulty: " + mission_data.difficulty
	mission_button.custom_minimum_size = Vector2(300, 60)
	mission_button.pressed.connect(_on_mission_selected.bind(mission_data))
	
	# Style the button based on difficulty
	var style = StyleBoxFlat.new()
	match mission_data.difficulty:
		"Beginner":
			style.bg_color = Color.GREEN * 0.3
		"Intermediate":
			style.bg_color = Color.YELLOW * 0.3
		"Advanced":
			style.bg_color = Color.RED * 0.3
		_:
			style.bg_color = Color.BLUE * 0.3
	
	mission_button.add_theme_stylebox_override("normal", style)
	mission_list.add_child(mission_button)

func _on_mission_selected(mission_data: Dictionary):
	"""Handle mission selection"""
	active_mission = mission_data
	current_mission_label.text = "Active: " + mission_data.title + "\n" + mission_data.description
	mission_selected.emit(mission_data)
	show_notification("Mission activated: " + mission_data.title)

func show_notification(message: String):
	"""Show a temporary notification"""
	notification_label.text = message
	notification_panel.visible = true
	
	# Auto-hide after 3 seconds
	var tween = create_tween()
	tween.tween_delay(3.0)
	tween.tween_callback(func(): notification_panel.visible = false)

func update_mission_display():
	"""Update the current mission display"""
	if active_mission:
		current_mission_label.text = "Active: " + active_mission.title + "\n" + active_mission.description
	else:
		current_mission_label.text = "No active mission"

func complete_mission(mission_id: String):
	"""Mark a mission as completed"""
	for i in range(current_missions.size()):
		if current_missions[i].id == mission_id:
			current_missions[i].status = "completed"
			show_notification("Mission Completed: " + current_missions[i].title)
			break
	
	# Clear active mission if it was completed
	if active_mission and active_mission.id == mission_id:
		active_mission = null
		update_mission_display()

func _on_close_pressed():
	"""Handle mobile close button"""
	mobile_closed.emit()

# Sample mission data structure for reference
func create_sample_missions():
	"""Create sample missions for testing"""
	var missions = [
		{
			"id": "mission_001",
			"title": "Network Discovery",
			"description": "Use nmap to scan the local network and identify active hosts.",
			"difficulty": "Beginner",
			"tools_required": ["nmap"],
			"objectives": [
				"Scan IP range ***********/24",
				"Identify at least 3 active hosts",
				"Document open ports on target systems"
			],
			"status": "available"
		},
		{
			"id": "mission_002", 
			"title": "Password Analysis",
			"description": "Analyze password strength and crack weak passwords.",
			"difficulty": "Intermediate",
			"tools_required": ["hashcat", "john"],
			"objectives": [
				"Analyze password hashes",
				"Crack at least 2 weak passwords",
				"Generate security report"
			],
			"status": "locked"
		},
		{
			"id": "mission_003",
			"title": "Social Engineering",
			"description": "Conduct a phishing simulation and analyze results.",
			"difficulty": "Advanced", 
			"tools_required": ["social_engineer_toolkit"],
			"objectives": [
				"Create convincing phishing email",
				"Track click-through rates",
				"Provide security awareness training"
			],
			"status": "locked"
		}
	]
	
	for mission in missions:
		add_mission(mission)
