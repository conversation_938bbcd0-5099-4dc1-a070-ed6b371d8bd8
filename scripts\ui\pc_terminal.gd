extends Control
class_name PCTerminal

# PC Terminal interface for cybersecurity tool execution
# Simulates a real terminal where players execute cybersecurity commands

@onready var terminal_output: RichTextLabel = $Background/VBox/TerminalOutput
@onready var command_input: LineEdit = $Background/VBox/HBox/CommandInput
@onready var execute_button: Button = $Background/VBox/HBox/ExecuteButton
@onready var close_button: Button = $Background/VBox/HeaderHBox/CloseButton
@onready var help_button: Button = $Background/VBox/HeaderHBox/HelpButton
@onready var clear_button: Button = $Background/VBox/HeaderHBox/ClearButton

var cyber_tools_manager: CyberToolsManager
var command_history: Array = []
var history_index: int = -1
var current_directory: String = "/home/<USER>"

signal terminal_closed()
signal command_executed(command: String, result: Dictionary)

func _ready():
	# Connect signals
	execute_button.pressed.connect(_on_execute_pressed)
	close_button.pressed.connect(_on_close_pressed)
	help_button.pressed.connect(_on_help_pressed)
	clear_button.pressed.connect(_on_clear_pressed)
	command_input.text_submitted.connect(_on_command_submitted)
	
	# Setup terminal
	initialize_terminal()
	command_input.grab_focus()

func initialize_terminal():
	"""Initialize the terminal with welcome message"""
	add_output("[color=green]Cyber Tron Security Terminal v1.0[/color]")
	add_output("[color=yellow]Welcome to the cybersecurity training environment![/color]")
	add_output("Type 'help' for available commands or 'tools' to see available security tools.")
	add_output("")
	show_prompt()

func _input(event):
	if visible and event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_UP:
				navigate_history(-1)
			KEY_DOWN:
				navigate_history(1)
			KEY_ESCAPE:
				_on_close_pressed()

func navigate_history(direction: int):
	"""Navigate through command history"""
	if command_history.is_empty():
		return
	
	history_index += direction
	history_index = clamp(history_index, -1, command_history.size() - 1)
	
	if history_index >= 0:
		command_input.text = command_history[history_index]
		command_input.caret_column = command_input.text.length()
	else:
		command_input.text = ""

func _on_command_submitted(command: String):
	_on_execute_pressed()

func _on_execute_pressed():
	var command = command_input.text.strip_edges()
	if command.is_empty():
		return
	
	# Add to history
	if command not in command_history:
		command_history.append(command)
	history_index = -1
	
	# Show command in terminal
	add_output("[color=cyan]cybertron@security:~$ " + command + "[/color]")
	
	# Execute command
	execute_command(command)
	
	# Clear input
	command_input.text = ""
	command_input.grab_focus()

func execute_command(command: String):
	"""Execute a terminal command"""
	var parts = command.split(" ", false)
	if parts.is_empty():
		return
	
	var cmd = parts[0].to_lower()
	var args = parts.slice(1)
	
	match cmd:
		"help":
			show_help()
		"clear":
			clear_terminal()
		"tools":
			show_available_tools()
		"ls":
			execute_ls(args)
		"cd":
			execute_cd(args)
		"pwd":
			add_output(current_directory)
		"whoami":
			add_output("cybertron")
		"nmap":
			execute_nmap(args)
		"nc", "netcat":
			execute_netcat(args)
		"wireshark", "tshark":
			execute_wireshark(args)
		"hydra":
			execute_hydra(args)
		"sqlmap":
			execute_sqlmap(args)
		"msfconsole":
			execute_metasploit(args)
		_:
			add_output("[color=red]Command not found: " + cmd + "[/color]")
			add_output("Type 'help' for available commands.")
	
	show_prompt()

func show_help():
	"""Show help information"""
	add_output("[color=yellow]Available Commands:[/color]")
	add_output("  help          - Show this help message")
	add_output("  clear         - Clear the terminal")
	add_output("  tools         - Show available security tools")
	add_output("  ls            - List directory contents")
	add_output("  cd <dir>      - Change directory")
	add_output("  pwd           - Show current directory")
	add_output("  whoami        - Show current user")
	add_output("")
	add_output("[color=yellow]Security Tools:[/color]")
	add_output("  nmap          - Network discovery and security auditing")
	add_output("  nc/netcat     - Network utility for connections")
	add_output("  wireshark     - Network protocol analyzer")
	add_output("  hydra         - Password cracking tool")
	add_output("  sqlmap        - SQL injection testing tool")
	add_output("  msfconsole    - Metasploit framework")

func show_available_tools():
	"""Show available cybersecurity tools"""
	if not cyber_tools_manager:
		add_output("[color=red]Cyber tools manager not available[/color]")
		return
	
	var unlocked_tools = cyber_tools_manager.get_unlocked_tools()
	add_output("[color=yellow]Available Security Tools:[/color]")
	
	for tool_name in unlocked_tools:
		var tool = cyber_tools_manager.available_tools[tool_name]
		add_output("  [color=green]" + tool.name + "[/color] - " + tool.description)
	
	if unlocked_tools.is_empty():
		add_output("  No tools unlocked yet. Complete missions to unlock tools!")

func execute_ls(args: Array):
	"""Execute ls command"""
	add_output("total 8")
	add_output("drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 25 10:00 Documents")
	add_output("drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 25 10:00 Tools")
	add_output("-rw-r--r-- 1 <USER> <GROUP>  156 Dec 25 10:00 mission_notes.txt")

func execute_cd(args: Array):
	"""Execute cd command"""
	if args.is_empty():
		current_directory = "/home/<USER>"
	else:
		var target = args[0]
		if target == "..":
			# Go up one directory
			var parts = current_directory.split("/")
			if parts.size() > 2:
				parts.pop_back()
				current_directory = "/".join(parts)
		else:
			# Simple directory change
			current_directory = current_directory + "/" + target
	
	add_output("Changed to: " + current_directory)

func execute_nmap(args: Array):
	"""Execute nmap command"""
	if not cyber_tools_manager or "nmap" not in cyber_tools_manager.get_unlocked_tools():
		add_output("[color=red]nmap: command not found or not unlocked[/color]")
		return
	
	if args.is_empty():
		add_output("Usage: nmap [options] <target>")
		add_output("Examples:")
		add_output("  nmap -sn ***********/24    # Ping scan")
		add_output("  nmap -sS ***********       # SYN scan")
		add_output("  nmap -sV ***********       # Service version detection")
		return
	
	# Parse nmap arguments
	var command_type = "port_scan"  # default
	var target = "***********"     # default
	
	for i in range(args.size()):
		var arg = args[i]
		if arg == "-sn":
			command_type = "ping_scan"
		elif arg == "-sS":
			command_type = "port_scan"
		elif arg == "-sV":
			command_type = "service_scan"
		elif arg == "--script" and i + 1 < args.size() and args[i + 1] == "vuln":
			command_type = "vuln_scan"
		elif not arg.begins_with("-"):
			target = arg
	
	# Execute nmap simulation
	var result = cyber_tools_manager.execute_tool_command("nmap", command_type, {"target": target})
	
	if "error" in result:
		add_output("[color=red]" + result.error + "[/color]")
	else:
		add_output(result.output)
		command_executed.emit("nmap " + " ".join(args), result)

func execute_netcat(args: Array):
	"""Execute netcat command"""
	if not cyber_tools_manager or "netcat" not in cyber_tools_manager.get_unlocked_tools():
		add_output("[color=red]netcat: command not found or not unlocked[/color]")
		return
	
	if args.is_empty():
		add_output("Usage: nc [options] <host> <port>")
		add_output("Examples:")
		add_output("  nc -l -p 4444              # Listen on port 4444")
		add_output("  nc *********** 22          # Connect to host on port 22")
		return
	
	# Simple netcat simulation
	var result = cyber_tools_manager.execute_tool_command("netcat", "banner_grab", {})
	add_output(result.get("output", "Netcat executed"))

func execute_wireshark(args: Array):
	"""Execute wireshark/tshark command"""
	if not cyber_tools_manager or "wireshark" not in cyber_tools_manager.get_unlocked_tools():
		add_output("[color=red]wireshark: command not found or not unlocked[/color]")
		return
	
	var result = cyber_tools_manager.execute_tool_command("wireshark", "capture", {})
	add_output(result.get("output", "Wireshark executed"))

func execute_hydra(args: Array):
	"""Execute hydra command"""
	if not cyber_tools_manager or "hydra" not in cyber_tools_manager.get_unlocked_tools():
		add_output("[color=red]hydra: command not found or not unlocked[/color]")
		return
	
	var result = cyber_tools_manager.execute_tool_command("hydra", "ssh_brute", {})
	add_output(result.get("output", "Hydra executed"))

func execute_sqlmap(args: Array):
	"""Execute sqlmap command"""
	if not cyber_tools_manager or "sqlmap" not in cyber_tools_manager.get_unlocked_tools():
		add_output("[color=red]sqlmap: command not found or not unlocked[/color]")
		return
	
	var result = cyber_tools_manager.execute_tool_command("sqlmap", "basic_scan", {})
	add_output(result.get("output", "SQLMap executed"))

func execute_metasploit(args: Array):
	"""Execute metasploit command"""
	if not cyber_tools_manager or "metasploit" not in cyber_tools_manager.get_unlocked_tools():
		add_output("[color=red]msfconsole: command not found or not unlocked[/color]")
		return
	
	var result = cyber_tools_manager.execute_tool_command("metasploit", "console", {})
	add_output(result.get("output", "Metasploit executed"))

func add_output(text: String):
	"""Add text to terminal output"""
	terminal_output.append_text(text + "\n")
	# Auto-scroll to bottom
	await get_tree().process_frame
	terminal_output.scroll_to_line(terminal_output.get_line_count())

func show_prompt():
	"""Show command prompt"""
	add_output("")

func clear_terminal():
	"""Clear terminal output"""
	terminal_output.clear()
	initialize_terminal()

func _on_close_pressed():
	"""Handle terminal close"""
	terminal_closed.emit()

func _on_help_pressed():
	"""Handle help button"""
	show_help()
	show_prompt()

func _on_clear_pressed():
	"""Handle clear button"""
	clear_terminal()

func set_cyber_tools_manager(manager: CyberToolsManager):
	"""Set the cyber tools manager reference"""
	cyber_tools_manager = manager
